#ifndef VIEWMODELASSEMBLER_H
#define VIEWMODELASSEMBLER_H

#include "PartViewModel.h"
#include "CategoriesViewModel.h"
#include "../partDb/sqlModel/partmodel.h"
#include "../globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "../globalDb/sqlCtrl/categoriesctrl.h"
#include "../globalDb/sqlCtrl/categoryattributelinksctrl.h"
#include "../../LibraryContext.h"

#include <QList>
#include <QString>

class ViewModelAssembler
{
public:
    /**
     * @brief 装配元器件视图模型
     * @param partModel             [输入] 从ptlib数据库中读取的原始PartModel
     * @param globalContext         [输入] 全局库上下文，用于访问属性定义控制器
     * @return                      一个包含了所有UI所需信息的完整ViewModel
     */
    static PartViewModel createViewModel(const PartModelPtr& partModel,
                                         const GlobalLibraryContext& globalContext);

    /**
     * @brief 装配分类视图模型
     * @param categoriesModel       [输入] 从全局数据库中读取的原始CategoriesModel
     * @param globalContext         [输入] 全局库上下文，包含所有必要的控制器
     * @return                      一个包含了所有UI所需信息的完整CategoriesViewModel
     */
    static CategoriesViewModel createCategoriesViewModel(const CategoriesModelPtr& categoriesModel,
                                                         const GlobalLibraryContext& globalContext);

    /**
     * @brief 装配元器件视图模型（通过ID查找）
     * @param partId                [输入] 元器件ID
     * @param globalContext         [输入] 全局库上下文，用于访问属性定义控制器
     * @param partLibraries         [输入] 零件库上下文列表，包含所有可能包含该元器件的库
     * @return                      一个包含了所有UI所需信息的完整PartViewModel
     */
    static PartViewModel createPartViewModel(const QString& partId,
                                             const GlobalLibraryContext& globalContext,
                                             const QList<PartLibraryContext>& partLibraries);

    /**
     * @brief 装配分类视图模型（通过ID查找）
     * @param categoryId            [输入] 分类ID
     * @param globalContext         [输入] 全局库上下文，包含所有必要的控制器
     * @return                      一个包含了所有UI所需信息的完整CategoriesViewModel
     */
    static CategoriesViewModel createCategoriesViewModel(const QString& categoryId,
                                                         const GlobalLibraryContext& globalContext);
};

#endif // VIEWMODELASSEMBLER_H
